defmodule Drops.Schema.InferenceTest do
  use Drops.ContractCase

  alias Drops.Schema.Inference
  import Drops.Type.DSL

  describe "Map inference - existing schema format" do
    test "infers schema from a simple map" do
      schema_map = %{
        required(:name) => string(),
        required(:age) => integer()
      }

      result = Inference.infer_schema(schema_map, [])

      assert result == schema_map
    end

    test "infers schema from a complex map" do
      schema_map = %{
        required(:name) => string(:filled?),
        optional(:email) => string(),
        required(:address) => %{
          required(:street) => string(),
          required(:city) => string(),
          optional(:zipcode) => string()
        }
      }

      result = Inference.infer_schema(schema_map, [])

      assert result == schema_map
    end

    test "handles empty map" do
      schema_map = %{}

      result = Inference.infer_schema(schema_map, [])

      assert result == %{}
    end
  end

  describe "Map inference - plain map conversion" do
    test "converts simple plain map to schema AST" do
      plain_map = %{name: :string, age: :integer}

      result = Inference.infer_schema(plain_map, [])

      expected =
        {:map,
         [
           {required(:name), type(:string)},
           {required(:age), type(:integer)}
         ]}

      assert result == expected
    end

    test "converts nested plain maps to schema AST" do
      plain_map = %{
        user: %{
          name: :string,
          age: :integer
        },
        company: :string
      }

      result = Inference.infer_schema(plain_map, [])

      expected =
        {:map,
         [
           {required(:user),
            {:map,
             [
               {required(:name), type(:string)},
               {required(:age), type(:integer)}
             ]}},
           {required(:company), type(:string)}
         ]}

      assert result == expected
    end

    test "handles mixed atom and non-atom values" do
      plain_map = %{
        name: :string,
        count: :integer,
        data: %{nested: :boolean}
      }

      result = Inference.infer_schema(plain_map, [])

      expected =
        {:map,
         [
           {required(:count), type(:integer)},
           {required(:data),
            {:map,
             [
               {required(:nested), type(:boolean)}
             ]}},
           {required(:name), type(:string)}
         ]}

      assert result == expected
    end

    test "does not convert maps that already have schema keys" do
      mixed_map = %{
        required(:name) => string(),
        optional(:age) => integer()
      }

      result = Inference.infer_schema(mixed_map, [])

      # Should return unchanged since it already has schema keys
      assert result == mixed_map
    end

    test "handles deeply nested plain maps" do
      plain_map = %{
        level1: %{
          level2: %{
            level3: :string
          }
        }
      }

      result = Inference.infer_schema(plain_map, [])

      expected =
        {:map,
         [
           {required(:level1),
            {:map,
             [
               {required(:level2),
                {:map,
                 [
                   {required(:level3), type(:string)}
                 ]}}
             ]}}
         ]}

      assert result == expected
    end
  end

  describe "Map inference - integration with schema compilation" do
    test "plain map can be compiled and used for validation" do
      plain_map = %{name: :string, age: :integer}

      # Test that the inferred schema can be compiled
      compiled_type = Drops.Schema.infer_and_compile(plain_map, [])

      assert %Drops.Types.Map{} = compiled_type
      assert length(compiled_type.keys) == 2

      # Test that the compiled type structure is correct
      key_names = Enum.map(compiled_type.keys, & &1.path) |> Enum.sort()
      assert key_names == [[:age], [:name]]

      # Test that all keys are required by default
      presences = Enum.map(compiled_type.keys, & &1.presence) |> Enum.uniq()
      assert presences == [:required]
    end

    test "nested plain map can be compiled and used for validation" do
      plain_map = %{
        user: %{
          name: :string,
          age: :integer
        }
      }

      compiled_type = Drops.Schema.infer_and_compile(plain_map, [])

      assert %Drops.Types.Map{} = compiled_type
      assert length(compiled_type.keys) == 1

      # Check that the nested structure is properly compiled
      [user_key] = compiled_type.keys
      assert user_key.path == [:user]
      assert user_key.presence == :required
      assert %Drops.Types.Map{} = user_key.type
      assert length(user_key.type.keys) == 2
    end

    test "plain map works end-to-end with contract creation" do
      # Create a test contract module dynamically
      defmodule TestPlainMapContract do
        use Drops.Contract

        # Use the inferred schema from a plain map
        plain_map = %{name: :string, age: :integer}
        inferred_schema = Drops.Schema.Inference.infer_schema(plain_map, [])

        schema do
          inferred_schema
        end
      end

      # Test successful validation
      assert {:ok, %{name: "John", age: 30}} =
               TestPlainMapContract.conform(%{name: "John", age: 30})

      # Test validation failure
      assert {:error, errors} = TestPlainMapContract.conform(%{name: "John", age: "30"})
      assert length(errors) == 1
    end
  end
end
