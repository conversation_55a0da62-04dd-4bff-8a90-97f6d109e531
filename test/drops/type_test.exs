defmodule Drops.TypeTest do
  use Drops.ContractCase
  use Drops.DoctestCase

  defmodule Email do
    use Drops.Type, string()
  end

  defmodule FilledEmail do
    use Drops.Type, string(:filled?)
  end

  defmodule User do
    use Drops.Type, %{
      required(:name) => string(),
      required(:email) => string()
    }
  end

  defmodule Price do
    use Drops.Type, union([:integer, :float], gt?: 0)
  end

  doctest Drops.Type

  describe "type registry" do
    test "registered_types/0 returns list of all registered types" do
      # Ensure built-in types are loaded by referencing them
      _ = Drops.Types.Primitive
      _ = Drops.Types.Union
      _ = Drops.Types.Number
      _ = Drops.Types.List
      _ = Drops.Types.Cast
      _ = Drops.Types.Map

      types = Drops.Type.registered_types()

      # Built-in types should be registered
      assert Drops.Types.Primitive in types
      assert Drops.Types.Union in types
      assert Drops.Types.Number in types
      assert Drops.Types.List in types
      assert Drops.Types.Cast in types
      assert Drops.Types.Map in types

      # Custom types defined in this module should be registered
      assert Email in types
      assert FilledEmail in types
      assert User in types
      assert Price in types
    end

    test "type?/1 returns true for registered Drops types" do
      # Ensure built-in types are loaded by referencing them
      _ = Drops.Types.Primitive
      _ = Drops.Types.Union
      _ = Drops.Types.Number
      _ = Drops.Types.List
      _ = Drops.Types.Cast
      _ = Drops.Types.Map

      # Built-in types
      assert Drops.Type.type?(Drops.Types.Primitive)
      assert Drops.Type.type?(Drops.Types.Union)
      assert Drops.Type.type?(Drops.Types.Number)
      assert Drops.Type.type?(Drops.Types.List)
      assert Drops.Type.type?(Drops.Types.Cast)
      assert Drops.Type.type?(Drops.Types.Map)

      # Custom types
      assert Drops.Type.type?(Email)
      assert Drops.Type.type?(FilledEmail)
      assert Drops.Type.type?(User)
      assert Drops.Type.type?(Price)
    end

    test "type?/1 returns false for non-Drops types" do
      refute Drops.Type.type?(String)
      refute Drops.Type.type?(Integer)
      refute Drops.Type.type?(Map)
      refute Drops.Type.type?(List)
      refute Drops.Type.type?(Atom)
      refute Drops.Type.type?(Process)
      refute Drops.Type.type?(GenServer)
    end

    test "register_type/1 adds new types to registry" do
      # Define a new type at runtime
      defmodule RuntimeType do
        use Drops.Type, string()
      end

      # It should be automatically registered
      assert Drops.Type.type?(RuntimeType)
      assert RuntimeType in Drops.Type.registered_types()
    end
  end
end
